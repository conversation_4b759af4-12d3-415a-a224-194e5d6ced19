# ChatService Improvements - Bot Access Enhancement

## Vấn đề ban đầu

<PERSON>ơ<PERSON> thức `getActiveChatBots()` trong `ChatService.php` chỉ lấy được bot do chính user tạo ra, thiếu các trường hợp:
- Bot được chia sẻ trực tiếp với user
- Bot từ organization mà user là thành viên
- Bot từ organization mà user được mời (pending invitation)

## Giải pháp đã triển khai

### 1. Cập nhật phương thức `getActiveChatBots()`

**Trước:**
```php
->where([
    'owner_id' => auth()->id(),
    'owner_type' => get_class(auth()->user())
])
```

**Sau:**
```php
->where(function ($query) use ($userId, $userClass) {
    // 1. Bots owned by the user
    $query->where(function ($q) use ($userId, $userClass) {
        $q->where('owner_id', $userId)
          ->where('owner_type', $userClass);
    })
    // 2. Bots shared directly with the user
    ->orWhereHas('shares', function ($q) use ($userId) {
        $q->where('user_id', $userId)
          ->where('status', 'active');
    })
    // 3. Bots from organizations where user is a member
    ->orWhere(function ($q) use ($userId) {
        $q->where('owner_type', 'Modules\\Organization\\Models\\Organization')
          ->whereHas('owner', function ($orgQuery) use ($userId) {
              $orgQuery->whereHas('members', function ($memberQuery) use ($userId) {
                  $memberQuery->where('user_id', $userId);
              });
          });
    })
    // 4. Bots from organizations where user has pending invitations
    ->orWhere(function ($q) use ($userId) {
        $q->where('owner_type', 'Modules\\Organization\\Models\\Organization')
          ->whereHas('owner', function ($orgQuery) use ($userId) {
              $orgQuery->whereHas('invitations', function ($inviteQuery) use ($userId) {
                  $inviteQuery->where('email', auth()->user()->email)
                             ->where('expires_at', '>', now());
              });
          });
    });
})
```

### 2. Tách logic thành method riêng

- Tạo method private `getAccessibleBots()` để xử lý logic phức tạp
- Giữ nguyên caching logic trong `getActiveChatBots()`
- Cải thiện cache key để unique theo user: `'bots.active.list.user.' . auth()->id()`

### 3. Sử dụng scope và enum

- Thay `->where('status', 'active')` bằng `->active()` scope
- Import `BotStatus` enum để đảm bảo type safety

## Các trường hợp bot access được hỗ trợ

### 1. Bot do user sở hữu
```sql
WHERE owner_id = {user_id} AND owner_type = 'Modules\User\Models\User'
```

### 2. Bot được chia sẻ trực tiếp
```sql
WHERE EXISTS (
    SELECT 1 FROM bot_shares 
    WHERE bot_id = bots.id 
    AND user_id = {user_id} 
    AND status = 'active'
)
```

### 3. Bot từ organization (user là thành viên)
```sql
WHERE owner_type = 'Modules\Organization\Models\Organization'
AND EXISTS (
    SELECT 1 FROM organization_members 
    WHERE organization_id = bots.owner_id 
    AND user_id = {user_id}
)
```

### 4. Bot từ organization (user có lời mời pending)
```sql
WHERE owner_type = 'Modules\Organization\Models\Organization'
AND EXISTS (
    SELECT 1 FROM organization_invitations
    WHERE organization_id = bots.owner_id
    AND email = '{user_email}'
    AND expires_at > NOW()
)
```

**Lưu ý về Guest Role:**
- Khi user chấp nhận lời mời, họ có thể chọn:
  - `accept()`: Trở thành member với role được mời (admin, editor, viewer, member)
  - `acceptAsGuest()`: Trở thành member với role `guest` (quyền hạn chế)
- Sau khi chấp nhận, lời mời sẽ bị xóa và user trở thành member (Case 3)
- Case 4 chỉ áp dụng cho những user chưa chấp nhận lời mời

## Cải thiện hiệu năng

1. **Eager loading**: Load trước `aiModel` và `owner` relationships
2. **Selective fields**: Chỉ select các field cần thiết cho owner
3. **Proper indexing**: Đảm bảo các foreign key đã được index
4. **Caching**: Cache kết quả theo user để tránh query lặp lại

## Testing

Để test các thay đổi:

```bash
# Trong Laravel environment
php artisan tinker

# Test basic functionality
$service = new \Modules\ChatBot\Services\ChatService();
$bots = $service->getActiveChatBots();
dd($bots->count());

# Test với user có bot shared
Auth::loginUsingId(2);
$bots = $service->getActiveChatBots();
dd($bots->pluck('name'));
```

## Lưu ý quan trọng

1. **Cache invalidation**: Khi có thay đổi về bot sharing hoặc organization membership, cần clear cache
2. **Permission checking**: Method này chỉ lấy danh sách bot, cần check permission riêng khi thực hiện actions
3. **Performance monitoring**: Monitor query performance với large dataset
4. **Security**: Đảm bảo không leak thông tin bot của organization khác

## Files đã thay đổi

- `Modules/ChatBot/app/Services/ChatService.php`: Cập nhật logic chính
- `test_chat_service.php`: Script test functionality
- `CHATSERVICE_IMPROVEMENTS.md`: Documentation này
