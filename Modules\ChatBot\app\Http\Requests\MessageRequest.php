<?php

namespace Modules\ChatBot\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modu<PERSON>\ChatBot\Enums\ContentType;

class MessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'conversation_uuid' => ['string', 'exists:conversations,uuid'],
            'content' => ['required_without:attachments', 'nullable', 'string', 'max:50000'],
            'content_type' => ['nullable', Rule::enum(ContentType::class)],
            'attachments' => ['nullable', 'array'],
            'attachments.*' => ['array'],
            'attachments.*.type' => ['required_with:attachments.*', 'string', 'in:image,file,audio,video'],
            'attachments.*.url' => ['required_with:attachments.*', 'string', 'url'],
            'attachments.*.name' => ['required_with:attachments.*', 'string', 'max:255'],
            'attachments.*.size' => ['nullable', 'integer', 'min:0'],
            'attachments.*.mime_type' => ['nullable', 'string', 'max:100'],
            'metadata' => ['nullable', 'array'],
            'quality_score' => ['nullable', 'integer', 'min:1', 'max:5'],
            'is_helpful' => ['nullable', 'boolean'],
        ];
    }
}
