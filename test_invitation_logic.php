<?php

/**
 * Test script để kiểm tra logic invitation và guest role
 * Chạy trong Laravel environment: php artisan tinker < test_invitation_logic.php
 */

echo "Testing Organization Invitation & Guest Role Logic...\n";
echo "===================================================\n\n";

// Test 1: Check available roles
echo "=== Test 1: Available Roles ===\n";
try {
    $memberRoles = \Modules\Organization\Models\OrganizationMember::ROLES;
    $invitationRoles = \Modules\Organization\Models\OrganizationInvitation::ROLES;
    
    echo "✅ OrganizationMember roles: " . implode(', ', array_keys($memberRoles)) . "\n";
    echo "✅ OrganizationInvitation roles: " . implode(', ', array_keys($invitationRoles)) . "\n";
    
    if (array_key_exists('guest', $memberRoles)) {
        echo "✅ Guest role is supported in OrganizationMember\n";
    } else {
        echo "❌ Guest role is missing in OrganizationMember\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking roles: " . $e->getMessage() . "\n";
}

// Test 2: Check invitation acceptance logic
echo "\n=== Test 2: Invitation Acceptance Logic ===\n";
try {
    // Check if methods exist
    if (method_exists(\Modules\Organization\Models\OrganizationInvitation::class, 'accept')) {
        echo "✅ accept() method exists\n";
    }
    
    if (method_exists(\Modules\Organization\Models\OrganizationInvitation::class, 'acceptAsGuest')) {
        echo "✅ acceptAsGuest() method exists\n";
    }
    
    if (method_exists(\Modules\Organization\Models\OrganizationMember::class, 'isGuest')) {
        echo "✅ isGuest() method exists\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking methods: " . $e->getMessage() . "\n";
}

// Test 3: Check database stats
echo "\n=== Test 3: Database Statistics ===\n";
try {
    $totalInvitations = \Modules\Organization\Models\OrganizationInvitation::count();
    $validInvitations = \Modules\Organization\Models\OrganizationInvitation::valid()->count();
    $expiredInvitations = \Modules\Organization\Models\OrganizationInvitation::expired()->count();
    
    echo "✅ Total invitations: {$totalInvitations}\n";
    echo "✅ Valid invitations: {$validInvitations}\n";
    echo "✅ Expired invitations: {$expiredInvitations}\n";
    
    $totalMembers = \Modules\Organization\Models\OrganizationMember::count();
    $guestMembers = \Modules\Organization\Models\OrganizationMember::where('role', 'guest')->count();
    
    echo "✅ Total organization members: {$totalMembers}\n";
    echo "✅ Guest members: {$guestMembers}\n";
} catch (Exception $e) {
    echo "❌ Error checking database: " . $e->getMessage() . "\n";
}

// Test 4: Test ChatService with current user
echo "\n=== Test 4: ChatService Bot Access ===\n";
try {
    if (auth()->check()) {
        $user = auth()->user();
        echo "✅ Testing with user: {$user->email} (ID: {$user->id})\n";
        
        $service = new \Modules\ChatBot\Services\ChatService();
        $bots = $service->getActiveChatBots();
        
        echo "✅ Accessible bots: {$bots->count()}\n";
        
        // Check bot access breakdown
        $ownedBots = \Modules\ChatBot\Models\Bot::where('owner_id', $user->id)
                                                 ->where('owner_type', get_class($user))
                                                 ->active()
                                                 ->count();
        
        $sharedBots = \Modules\ChatBot\Models\Bot::whereHas('shares', function($q) use ($user) {
                                                     $q->where('user_id', $user->id)
                                                       ->where('status', 'active');
                                                 })
                                                 ->active()
                                                 ->count();
        
        echo "  - Owned bots: {$ownedBots}\n";
        echo "  - Shared bots: {$sharedBots}\n";
        
        // Check organization memberships
        $memberOrgs = \Modules\Organization\Models\OrganizationMember::where('user_id', $user->id)->count();
        echo "  - Organization memberships: {$memberOrgs}\n";
        
        // Check pending invitations
        $pendingInvites = \Modules\Organization\Models\OrganizationInvitation::where('email', $user->email)
                                                                             ->valid()
                                                                             ->count();
        echo "  - Pending invitations: {$pendingInvites}\n";
        
    } else {
        echo "⚠️  No user authenticated. Please login first.\n";
        echo "   Run: Auth::loginUsingId(1);\n";
    }
} catch (Exception $e) {
    echo "❌ Error testing ChatService: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "📝 Summary of Invitation Logic:\n";
echo "1. User receives invitation → stored in organization_invitations\n";
echo "2. User can accept() → becomes member with invited role\n";
echo "3. User can acceptAsGuest() → becomes member with 'guest' role\n";
echo "4. After acceptance → invitation deleted, user added to organization_members\n";
echo "5. ChatService covers both pending invitations AND accepted members (including guests)\n\n";

echo "🔍 To test invitation flow:\n";
echo "1. Create invitation: \$org->inviteUser('<EMAIL>', 'member', \$inviter)\n";
echo "2. Accept as guest: \$invitation->acceptAsGuest(\$user)\n";
echo "3. Check bot access: \$service->getActiveChatBots()\n";
