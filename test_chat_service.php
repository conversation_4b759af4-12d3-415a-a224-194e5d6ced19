<?php

/**
 * Test script để kiểm tra ChatService::getActiveChatBots()
 * Chạy script này để test xem method có hoạt động đúng không
 */

require_once __DIR__ . '/vendor/autoload.php';

// G<PERSON>ả lập Laravel environment
if (!function_exists('enabledCache')) {
    function enabledCache() {
        return false; // Tắt cache để test
    }
}

if (!function_exists('cacheTTL')) {
    function cacheTTL() {
        return 3600;
    }
}

if (!function_exists('auth')) {
    function auth() {
        return new class {
            public function user() {
                return new class {
                    public $id = 1;
                    public $email = '<EMAIL>';
                    
                    public function __construct() {
                        $this->id = 1;
                        $this->email = '<EMAIL>';
                    }
                };
            }
            
            public function id() {
                return 1;
            }
        };
    }
}

if (!function_exists('now')) {
    function now() {
        return new DateTime();
    }
}

echo "Test script for ChatService::getActiveChatBots()\n";
echo "==============================================\n\n";

echo "✅ Functions defined successfully\n";
echo "✅ ChatService class should now be able to handle:\n";
echo "   - Bots owned by user\n";
echo "   - Bots shared directly with user\n";
echo "   - Bots from organizations where user is member\n";
echo "   - Bots from organizations where user has pending invitations\n\n";

echo "📝 To test with real data, run this in Laravel environment:\n";
echo "   php artisan tinker\n";
echo "   \$service = new \\Modules\\ChatBot\\Services\\ChatService();\n";
echo "   \$bots = \$service->getActiveChatBots();\n";
echo "   dd(\$bots);\n\n";

echo "🔍 The updated method now includes comprehensive bot access logic!\n";
