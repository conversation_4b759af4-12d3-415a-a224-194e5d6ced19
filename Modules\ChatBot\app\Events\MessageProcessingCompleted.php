<?php

namespace Modules\ChatBot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\ChatBot\Models\Message;

/**
 * Event fired when message processing completes
 */
class MessageProcessingCompleted implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Message $message,
        public array $response = []
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('conversation.' . $this->message->conversation->uuid),
            new PrivateChannel('user.' . $this->message->conversation->owner_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event' => 'message.processing.completed',
            'message_id' => $this->message->uuid,
            'conversation_id' => $this->message->conversation->uuid,
            'content' => $this->message->content,
            'status' => $this->message->status->value,
            'tokens' => $this->message->total_tokens,
            'cost' => $this->message->cost,
            'response_time_ms' => $this->message->response_time_ms,
            'timestamp' => now()->toISOString(),
            'completed_at' => $this->message->completed_at?->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.processing.completed';
    }
}
