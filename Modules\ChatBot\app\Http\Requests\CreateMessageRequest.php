<?php

namespace Modules\ChatBot\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\ContentType;
use Mo<PERSON><PERSON>\ChatBot\Models\Conversation;

class CreateMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'conversation_uuid' => 'nullable|string|exists:conversations,uuid',
            'role' => ['required', Rule::enum(MessageRole::class)],
            'content' => 'required|string|max:50000',
            'content_type' => ['nullable', Rule::enum(ContentType::class)],
            'attachments' => 'nullable|array|max:10',
            'attachments.*' => 'file|max:10240', // 10MB per file
            'tool_calls' => 'nullable|array',
            'tool_call_id' => 'nullable|string|max:255',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'conversation_uuid.required' => 'Conversation UUID là bắt buộc.',
            'conversation_uuid.exists' => 'Cuộc trò chuyện không tồn tại.',
            'role.required' => 'Vai trò tin nhắn là bắt buộc.',
            'content.required' => 'Nội dung tin nhắn là bắt buộc.',
            'content.max' => 'Nội dung tin nhắn không được vượt quá 50,000 ký tự.',
            'attachments.max' => 'Không được đính kèm quá 10 file.',
            'attachments.*.file' => 'Tệp đính kèm không hợp lệ.',
            'attachments.*.max' => 'Mỗi tệp đính kèm không được vượt quá 10MB.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Get conversation UUID from request data or route parameter
            $conversationUuid = $this->conversation_uuid ?? $this->route('uuid');

            if ($conversationUuid) {
                $user = auth()->user();
                $conversation = Conversation::where('uuid', $conversationUuid)->first();

                if ($conversation) {
                    // Check if user can access this conversation
                    if ($conversation->user_id !== $user->id || $conversation->user_type !== get_class($user)) {
                        // Also check if user can access the bot
                        if (!$conversation->bot->canBeAccessedBy($user->id)) {
                            $validator->errors()->add('conversation_uuid', 'Bạn không có quyền truy cập cuộc trò chuyện này.');
                        }
                    }

                    // Check if conversation is editable
                    if (!$conversation->status->isEditable()) {
                        $validator->errors()->add('conversation_uuid', 'Cuộc trò chuyện này không thể chỉnh sửa.');
                    }
                }
            }

            // Validate content based on content type
            if ($this->content_type && $this->attachments) {
                $contentType = ContentType::from($this->content_type);
                
                if ($contentType === ContentType::TEXT && !empty($this->attachments)) {
                    $validator->errors()->add('attachments', 'Tin nhắn văn bản không được có tệp đính kèm.');
                }
                
                if ($contentType !== ContentType::TEXT && empty($this->attachments)) {
                    $validator->errors()->add('attachments', 'Loại nội dung này yêu cầu tệp đính kèm.');
                }
            }

            // Validate role restrictions
            if ($this->role) {
                $role = MessageRole::from($this->role);
                
                // Only allow user and system roles for manual creation
                if (!in_array($role, [MessageRole::USER, MessageRole::SYSTEM])) {
                    $validator->errors()->add('role', 'Chỉ được tạo tin nhắn với vai trò user hoặc system.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $this->merge([
            'role' => $this->role ?? MessageRole::USER->value,
            'content_type' => $this->content_type ?? ContentType::TEXT->value,
        ]);
    }
}
