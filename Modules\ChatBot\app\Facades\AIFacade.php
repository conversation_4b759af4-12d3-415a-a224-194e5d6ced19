<?php

namespace Modules\ChatBot\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * AI Facade
 *
 * Provides convenient access to public bot functionality through a static interface.
 * Only exposes public/user-level methods. Administrative methods should be accessed
 * through direct service injection.
 *
 *
 * @see \Modules\ChatBot\Services\AIService
 */
class AIFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'ai.service';
    }
}
