<?php

namespace Modules\ChatBot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\ChatBot\Models\Message;
use Exception;

/**
 * Event fired when message processing fails
 */
class MessageProcessingFailed implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Message $message,
        public Exception $exception
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('conversation.' . $this->message->conversation->uuid),
            new PrivateChannel('user.' . $this->message->conversation->owner_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event' => 'message.processing.failed',
            'message_id' => $this->message->uuid,
            'conversation_id' => $this->message->conversation->uuid,
            'error' => $this->exception->getMessage(),
            'status' => $this->message->status->value,
            'timestamp' => now()->toISOString(),
            'can_retry' => $this->message->status->canBeRetried(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.processing.failed';
    }
}
