<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\ChatBot\Models\Bot;
use Modules\Core\Traits\ResponseTrait;

class ChatController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {

    }

    /**
     * Display a listing of user's accessible bots.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();

            $bots = Bot::query()
                ->with(['aiModel:id,key,name', 'owner' => function ($query) {
                    $query->select(['id', 'first_name', 'last_name', 'full_name', 'avatar']);
                }])
                ->where([
                    'owner_id' => auth()->id(),
                    'owner_type' => get_class($user)
                ])
                ->orderByDesc('updated_at')
                ->get()
                ->map(function (Bot $bot) {
                    $bot->aiModel->makeHidden(['id']);
                    $bot->owner->makeHidden(['id']);
                    return $bot->makeHidden(['id', 'model_ai_id', 'owner_id', 'owner_type']);
                });

            return $this->successResponse($bots);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
}
