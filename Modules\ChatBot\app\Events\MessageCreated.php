<?php

namespace Modules\ChatBot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\ChatBot\Models\Message;

/**
 * Event fired when a new message is created
 */
class MessageCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Message $message
    ) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('conversation.' . $this->message->conversation->uuid),
            new PrivateChannel('user.' . $this->message->conversation->owner_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event' => 'message.created',
            'message' => [
                'id' => $this->message->uuid,
                'conversation_id' => $this->message->conversation->uuid,
                'role' => $this->message->role->value,
                'content' => $this->message->content,
                'content_type' => $this->message->content_type->value,
                'status' => $this->message->status->value,
                'attachments' => $this->message->attachments,
                'created_at' => $this->message->created_at->toISOString(),
            ],
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'message.created';
    }
}
